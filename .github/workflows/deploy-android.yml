name: "🤖Build & Upload <PERSON>eu<PERSON>uer Android App"
run-name: "🤖🚀 #${{ github.run_number }} | 🌍${{ inputs.APP_ENVIRONMENT }} 🛠${{ github.ref_name }} 👥${{ github.actor }}"

on:
  workflow_dispatch:
    inputs:
      APP_ENVIRONMENT:
        type: choice
        description: 'Select app environment'
        options:
          - 'daily'
          - 'staging'
          - 'production'
      ANDROID_DEPLOYMENT:
        type: choice
        description: '(android) Select to deploy self hosted or via github provided runners'
        options:
          - 'github-provided'
          - 'self-hosted'
      DEPLOYMENT:
        type: choice
        description: 'Select to deploy self hosted or via github provided runners (test and other steps)'
        options:
          - 'github-provided'
          - 'self-hosted'

env:
  SHARED_ACTIONS_BRANCH: 'main'
  RUBY_VERSION: '3.3.4'

jobs:
  android:
    name: "Build & Deploy Android 🤖"
    runs-on: ${{ inputs.ANDROID_DEPLOYMENT == 'self-hosted' && 'mercury' || 'ubuntu-24.04' }}
    environment: ${{ inputs.APP_ENVIRONMENT }}
    env:
      VERSION: ${{ needs.prerequisites.outputs.version }}
      BUILD_NUMBER: ${{ needs.prerequisites.outputs.build-number }}
      BUILD_TIMESTAMP: ${{ needs.prerequisites.outputs.build-timestamp }}
    steps:
      - name: setup github ci ssh key
        uses: webfactory/ssh-agent@v0.9.0
        if: ${{ inputs.ANDROID_DEPLOYMENT != 'self-hosted' }}
        with:
          ssh-private-key: ${{ secrets.SSH_GITHUB_CI }}

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: checkout shared actions repo
        uses: actions/checkout@v4
        with:
          repository: BYTEPOETS/bytepoets-shared-actions
          ref: ${{ env.SHARED_ACTIONS_BRANCH }}
          ssh-key: ${{ secrets.SSH_GITHUB_CI }}
          path: .github/actions/bytepoets-shared-actions

      - name: generate version and build timestamp
        id: gen-version
        uses: ./.github/actions/bytepoets-shared-actions/generate-version-and-build-number

      - name: generate build number
        id: gen-build-number
        run: |
          echo "build-number=$(date +'%Y%m%d%H%M%S')" >> $GITHUB_OUTPUT
        shell: bash

      - name: set java version
        uses: ./.github/actions/bytepoets-shared-actions/set-java-version
        with:
          use-java-17: 'true'
          install-java: ${{ inputs.ANDROID_DEPLOYMENT == 'self-hosted' && 'false' || 'true' }}

      - name: adjust android signing
        uses: ./.github/actions/bytepoets-shared-actions/android-signing
        with:
          signing-key-alias: ${{ secrets.NEUBAUER_ANDROID_KEY_ALIAS }}
          signing-keystore-password: ${{ secrets.NEUBAUER_ANDROID_KEYSTORE_PASSWORD }}
          signing-keystore-encoded: ${{ secrets.NEUBAUER_ANDROID_KEYSTORE_ENCODED }}
          add-bundletool-env-variables: 'true'

      - name: compile app
        run: |
          ./gradlew clean compileDailyReleaseSources -PdisablePreDex
        shell: bash

      - name: verify app # TODO: check result needed? step at all needed?
        continue-on-error: true
        run: |
          ./gradlew lintDailyRelease detekt testDailyDebugUnitTest -PdisablePreDex
        shell: bash

      - name: build app
        run: |
          ./gradlew assembleRelease -PdisablePreDex
        shell: bash

      - name: check apk output
        continue-on-error: true
        run: |
          "ls -lAh app/build/outputs/apk/${{ inputs.APP_ENVIRONMENT }}/release"
        working-directory: ${{ github.workspace }}
        shell: bash

      - name: upload apk to apps.bytepoets.com
        uses: ./.github/actions/bytepoets-shared-actions/android-upload-to-apps-bytepoets
        with:
          upload-apk: 'true'
          upload-aab: 'false'
          project-folder: 'neubauer-android'
          build-apk-location: "app/build/outputs/apk/${{ inputs.APP_ENVIRONMENT }}/release/*-release.apk"
          apk-file-name: app-${{ inputs.APP_ENVIRONMENT }}-${{ steps.gen-version.outputs.version }}_${{ steps.gen-build-number.outputs.build-number }}-release.apk
          apps-bytepoets-ssh-user: ${{ secrets.APPS_BYTEPOETS_SSH_USER }}
          apps-bytepoets-ssh-key: ${{ secrets.APPS_BYTEPOETS_SSH_KEY }}

task copyGitHooks(type: Copy) {
    description "Copies the git hooks from ${rootDir}/config/git-hooks to the .git folder."
    from("${rootDir}/config/git-hooks") {
        include '**/*.sh'
        rename '(.*).sh', '$1'
    }
    into "${rootDir}/.git/hooks"
}

task installGitHooks(type: Exec) {
    description "Installs the pre-commit git hooks from ${rootDir}/config/git-hooks."
    group 'git hooks'
    workingDir rootDir
    commandLine 'chmod'
    args '-R', '+x', '.git/hooks/'
    dependsOn copyGitHooks
    doLast {
        println('Git hook installed successfully.')
    }
}

task preCommit(type: GradleBuild) {
    description "Runs all tasks needed to verify commit"
    group "git hooks"
    tasks = ["detekt"]
    doLast {
        println("Task preCommit finished successfully")
    }
}

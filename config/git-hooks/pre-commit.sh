#!/usr/bin/env bash
echo "Running detekt check..."

./gradlew preCommit
EXIT_CODE=$?

if [ $EXIT_CODE -ne 0 ]; then
	echo 1>&2 "*************************************************"
	echo 1>&2 "                  <PERSON><PERSON><PERSON> failed                  "
	echo 1>&2 "  Please fix the above issues before committing  "
	echo 1>&2 "*************************************************"
	exit $EXIT_CODE
else
    echo "Det<PERSON><PERSON> found no issues. Proceeding with commit."
fi

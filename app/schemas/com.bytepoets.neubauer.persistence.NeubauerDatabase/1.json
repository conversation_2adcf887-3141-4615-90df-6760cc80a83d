{"formatVersion": 1, "database": {"version": 1, "identityHash": "254a1023f0ffff48e98e432d571f5ca0", "entities": [{"tableName": "issues", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `externalId` TEXT NOT NULL, `contact<PERSON>erson` TEXT, `address` TEXT, `description` TEXT, `note` TEXT, `status` TEXT NOT NULL, `updatedAt` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `latestAssignmentDate` INTEGER, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "externalId", "columnName": "externalId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "address", "columnName": "address", "affinity": "TEXT", "notNull": false}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "note", "columnName": "note", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "latestAssignmentDate", "columnName": "latestAssignmentDate", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "technicians", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `firstName` TEXT NOT NULL, `lastName` TEXT NOT NULL, `phoneNumber` TEXT, `updatedAt` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "firstName", "columnName": "firstName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastName", "columnName": "lastName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "phoneNumber", "columnName": "phoneNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "accepted_issues", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `issueId` INTEGER NOT NULL, `technicianId` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "issueId", "columnName": "issueId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "technicianId", "columnName": "technicianId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "images", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER NOT NULL, `type` TEXT NOT NULL, `refId` INTEGER NOT NULL, `width` INTEGER NOT NULL, `height` INTEGER NOT NULL, `uploadedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "refId", "columnName": "refId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "width", "columnName": "width", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "height", "columnName": "height", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uploadedAt", "columnName": "uploadedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": false}, "indices": [], "foreignKeys": []}, {"tableName": "note", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `issueId` INTEGER NOT NULL, `note` TEXT NOT NULL, `date` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "issueId", "columnName": "issueId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "note", "columnName": "note", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '254a1023f0ffff48e98e432d571f5ca0')"]}}
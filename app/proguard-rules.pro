# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-keepattributes SourceFile,LineNumberTable # Keep file names/line numbers
-keep public class * extends java.lang.Exception # custom exceptions

-keepclassmembers class com.bytepoets.neubauer.dal.network.model.response.** { *; } # Keep all network model members
-keepclassmembers class com.bytepoets.neubauer.bl.model.** { *; } # Keep all network model members

<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="HardcodedText">


    <item
        android:id="@+id/search"
        android:title="@string/ACTION_SEARCH"
        android:icon="@mipmap/ic_search"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/info"
        android:title="Info"
        android:icon="@mipmap/ic_info"
        app:showAsAction="always" />

    <item
        android:id="@+id/crash"
        android:title="Crash App"
        app:showAsAction="never"
        android:visible="false" />
</menu>

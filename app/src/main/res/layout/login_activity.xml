<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    tools:showIn=".ui.LoginFragment">

    <requestFocus />


    <RelativeLayout
        android:id="@+id/login_coordinator"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:fitsSystemWindows="false">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/login_constraint_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:descendantFocusability="beforeDescendants"
            android:focusable="true"
            android:focusableInTouchMode="true">

            <ImageView
                android:id="@+id/login_logo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:src="@mipmap/logo"
                app:layout_constraintWidth_max="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/login_email"
                style="@style/TextInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:inputType="textEmailAddress"
                android:layout_marginTop="20dp"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                app:layout_constraintBottom_toTopOf="@id/login_password"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/login_logo">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/login_email_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/EMAIL_PLACEHOLDER"
                    android:inputType="textEmailAddress" />
            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/login_password"
                style="@style/TextInputLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:layout_marginTop="20dp"
                android:layout_marginStart="32dp"
                android:layout_marginEnd="32dp"
                app:layout_constraintBottom_toTopOf="@id/login_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/login_email">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/login_password_edit_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/PASSWORD"
                    android:inputType="textPassword" />
            </com.google.android.material.textfield.TextInputLayout>

            <Button
                android:id="@+id/login_button"
                style="@style/PrimaryButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/cell_margin_lr"
                android:layout_marginStart="@dimen/input_horizontal_margin"
                android:layout_marginEnd="@dimen/input_horizontal_margin"
                android:text="@string/LOGIN"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/login_password" />

            <com.pnikosis.materialishprogress.ProgressWheel
                android:id="@+id/login_progress_wheel"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_marginStart="16dp"
                android:elevation="50dp"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="@+id/login_button"
                app:layout_constraintStart_toStartOf="@+id/login_button"
                app:layout_constraintTop_toTopOf="@+id/login_button"
                app:matProg_barColor="@android:color/white"
                app:matProg_barWidth="2dp"
                app:matProg_progressIndeterminate="true" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/snackbar_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</RelativeLayout>

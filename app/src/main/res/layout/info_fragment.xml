<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/info"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.InfoFragment">

    <include
        android:id="@+id/app_bar_layout"
        layout="@layout/app_bar_layout"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/info_container_scroll"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="5dp"
        app:layout_constraintBottom_toTopOf="@id/sync_now_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar_layout">

        <LinearLayout
            android:id="@+id/info_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/input_horizontal_margin"
            android:layout_marginEnd="@dimen/input_horizontal_margin"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/VERSION"
                android:textAlignment="textStart"
                android:layout_marginTop="32dp"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/version_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/LOGGED_IN_AS"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/logged_in_as_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/LATEST_SYNC_SUCCESS"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/latest_sync_success_info"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/LATEST_SYNC_TRY"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_latest_sync_try"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/LATEST_SYNC_TIMESTAMP"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_latest_sync_timestamp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/NUMBER_OF_ISSUES"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_number_of_issues"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/NUMBER_OF_TECHNICIANS"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_number_of_technicians"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/NUMBER_OF_ACCEPTED_ISSUES"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_number_of_accepted_issues"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/NUMBER_OF_OPEN_IMAGE_UPLOADS"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_number_of_open_image_uploads"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/open_sans_extrabold"
                android:text="@string/NUMBER_OF_OPEN_NOTES_UPLOADS"
                android:textAlignment="textStart"
                android:textColor="@color/label" />

            <TextView
                android:id="@+id/info_label_number_of_open_note_uploads"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:fontFamily="@font/open_sans_regular"
                android:textAlignment="textStart"
                android:textSize="16dp" />

        </LinearLayout>
    </ScrollView>

    <Button
        android:id="@+id/sync_now_button"
        style="@style/PrimaryButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/input_horizontal_margin"
        android:layout_marginEnd="@dimen/input_horizontal_margin"
        android:layout_marginBottom="6dp"
        android:text="@string/SYNC_NOW"
        app:layout_constraintBottom_toTopOf="@id/logout_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.pnikosis.materialishprogress.ProgressWheel
        android:id="@+id/sync_now_progress_wheel"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="16dp"
        android:elevation="50dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/sync_now_button"
        app:layout_constraintStart_toStartOf="@+id/sync_now_button"
        app:layout_constraintTop_toTopOf="@+id/sync_now_button"
        app:matProg_barColor="@color/accent"
        app:matProg_barWidth="2dp"
        app:matProg_progressIndeterminate="true" />

    <Button
        android:id="@+id/logout_button"
        style="@style/SecondaryButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/input_horizontal_margin"
        android:layout_marginEnd="@dimen/input_horizontal_margin"
        android:layout_marginBottom="20dp"
        android:text="@string/LOGOUT"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.pnikosis.materialishprogress.ProgressWheel
        android:id="@+id/logout_progress_wheel"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="16dp"
        android:elevation="50dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/logout_button"
        app:layout_constraintStart_toStartOf="@+id/logout_button"
        app:layout_constraintTop_toTopOf="@+id/logout_button"
        app:matProg_barColor="@android:color/white"
        app:matProg_barWidth="2dp"
        app:matProg_progressIndeterminate="true" />


</androidx.constraintlayout.widget.ConstraintLayout>

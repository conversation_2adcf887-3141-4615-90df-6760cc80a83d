<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/app_bar_layout"
        layout="@layout/app_bar_layout_no_elevation"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/smartbricks_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="SmartBricks WebView Fragment"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/app_bar_layout" />

</androidx.constraintlayout.widget.ConstraintLayout>

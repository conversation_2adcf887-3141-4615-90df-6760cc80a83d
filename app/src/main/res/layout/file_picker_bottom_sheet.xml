<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/bottom_sheet_camera"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_margin="@dimen/cell_margin_lr"
        android:drawablePadding="@dimen/cell_padding_tb"
        android:backgroundTint="@android:color/white"
        android:gravity="start|center_vertical"
        android:text="@string/BOTTOM_SHEET_FILE_PICKER_CREATE_IMAGE"
        style="@style/NebText"
        app:drawableStartCompat="@mipmap/ic_photo_accent" />

    <TextView
        android:id="@+id/bottom_sheet_gallery"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginEnd="@dimen/cell_margin_lr"
        android:layout_marginStart="@dimen/cell_margin_lr"
        android:layout_marginBottom="@dimen/cell_margin_lr"
        android:drawablePadding="@dimen/cell_padding_tb"
        android:backgroundTint="@android:color/white"
        android:gravity="start|center_vertical"
        android:text="@string/BOTTOM_SHEET_FILE_PICKER_CHOOSE_IMAGE"
        style="@style/NebText"
        app:drawableStartCompat="@mipmap/ic_gallery_accent" />

</LinearLayout>

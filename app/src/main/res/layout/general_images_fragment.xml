<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/app_bar_layout"
            layout="@layout/app_bar_layout"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="parent" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/image_upload_button"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/app_bar_layout"
            app:layout_constraintVertical_bias="1.0">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/attachment_recyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/grid_layout_padding"
                android:clipToPadding="false" />
        </ScrollView>

        <Button
            android:id="@+id/image_upload_button"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="65dp"
            android:layout_margin="@dimen/cell_margin_lr"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

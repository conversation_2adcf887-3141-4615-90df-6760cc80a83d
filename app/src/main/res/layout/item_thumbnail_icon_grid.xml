<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="4dp"
    xmlns:tools="http://schemas.android.com/tools">

    <ImageView
        android:id="@+id/background_image_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1:1" />

    <ImageView
        android:id="@+id/icon_image_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:padding="65dp"
        app:tint="?colorAccent"
        tools:src="@drawable/ic_document"
        android:background="@drawable/gird_icon_border"
        app:layout_constraintStart_toStartOf="@+id/background_image_view"
        app:layout_constraintTop_toTopOf="@+id/background_image_view"
        app:layout_constraintEnd_toEndOf="@+id/background_image_view"
        app:layout_constraintBottom_toBottomOf="@+id/background_image_view"
        app:layout_constraintDimensionRatio="1:1" />

    <ImageButton
        android:id="@+id/delete_btn"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_margin="@dimen/cell_padding_tb"
        android:background="@mipmap/ic_delete"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

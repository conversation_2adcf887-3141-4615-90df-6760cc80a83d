<?xml version="1.0" encoding="utf-8"?>

<com.google.android.material.appbar.AppBarLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/app_bar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/Toolbar" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/issue_filter_bar_layout"
        style="@style/Widget.MaterialComponents.TabLayout.Colored"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:tabIndicatorHeight="@dimen/tab_indicator"
        app:tabSelectedTextColor="@color/white"
        app:tabTextColor="@color/label">

        <com.google.android.material.tabs.TabItem
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/STATUS_ACCEPTED" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/STATUS_OPEN" />

    </com.google.android.material.tabs.TabLayout>
</com.google.android.material.appbar.AppBarLayout>

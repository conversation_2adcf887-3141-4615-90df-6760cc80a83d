<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/info"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.InfoFragment">

    <include
        android:id="@+id/app_bar_layout"
        layout="@layout/app_bar_layout_no_elevation"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/issue_status_bar"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginTop="2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/app_bar_layout" />

    <TextView
        android:id="@+id/issue_status"
        style="@style/NebStatusLabel"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginEnd="32dp"
        app:layout_constraintBottom_toBottomOf="@+id/issue_status_bar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/issue_status_bar"
        tools:text="TextView" />

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/refresh_status"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/claim_button"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/issue_status_bar">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/info_container_scroll"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginBottom="5dp"
            app:layout_constraintBottom_toTopOf="@+id/claim_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/issue_status_bar">

            <LinearLayout
                android:id="@+id/info_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/input_horizontal_margin"
                android:layout_marginEnd="@dimen/input_horizontal_margin"
                android:orientation="vertical">

                <com.bytepoets.neubauer.ui.LabelContentView
                    android:id="@+id/customer_info"
                    style="@style/NebLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:titleText="@string/CUSTOMER" />

                <com.bytepoets.neubauer.ui.LabelContentView
                    android:id="@+id/issue_reporter_info"
                    style="@style/NebLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:titleText="@string/ISSUE_REPORTER" />

                <com.bytepoets.neubauer.ui.LabelContentView
                    android:id="@+id/work_info"
                    style="@style/NebLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:titleText="@string/WORK" />

                <com.bytepoets.neubauer.ui.LabelContentView
                    android:id="@+id/planned_date_info"
                    style="@style/NebLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:titleText="@string/PLANNED_DATE" />

                <com.bytepoets.neubauer.ui.LabelContentView
                    android:id="@+id/technician_info"
                    style="@style/NebLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:titleText="@string/TECHNICIAN"
                    app:useFallbackOnBlankString="true" />

                <com.bytepoets.neubauer.ui.LabelContentView
                    android:id="@+id/note_info"
                    style="@style/NebLabel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:titleText="@string/NOTE" />

                <TextView
                    android:id="@+id/note_not_sent"
                    style="@style/NebLabelContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/activity_vertical_margin"
                    android:layout_marginBottom="@dimen/activity_vertical_margin">

                    <ImageButton
                        android:id="@+id/add_note"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginEnd="12dp"
                        android:background="@drawable/gird_icon_border"
                        android:padding="65dp"
                        android:src="@mipmap/ic_new_comment"
                        android:tint="?colorAccent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <ImageButton
                        android:id="@+id/add_images"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="12dp"
                        android:background="@drawable/gird_icon_border"
                        android:padding="65dp"
                        android:src="@mipmap/ic_new_photo_v2"
                        android:tint="?colorAccent"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1:1"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/add_note"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_marginTop="-8dp"
                    android:layout_marginStart="-6dp"
                    android:layout_marginEnd="-6dp"
                    android:layout_marginBottom="@dimen/activity_vertical_margin"
                    android:nestedScrollingEnabled="false"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <Button
        android:id="@+id/claim_button"
        style="@style/PrimaryButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/input_horizontal_margin"
        android:layout_marginEnd="@dimen/input_horizontal_margin"
        android:layout_marginBottom="20dp"
        android:text="@string/ASSIGN_ISSUE"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <com.pnikosis.materialishprogress.ProgressWheel
        android:id="@+id/claim_progress_wheel"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginStart="16dp"
        android:elevation="50dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/claim_button"
        app:layout_constraintStart_toStartOf="@+id/claim_button"
        app:layout_constraintTop_toTopOf="@+id/claim_button"
        app:matProg_barColor="@android:color/white"
        app:matProg_barWidth="2dp"
        app:matProg_progressIndeterminate="true" />


</androidx.constraintlayout.widget.ConstraintLayout>

<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/app_bar_layout"
        layout="@layout/app_bar_layout_no_elevation"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/app_bar_layout">

        <EditText
            android:id="@+id/note"
            style="@style/NoteInputField"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_vertical_margin_double"
            android:layout_marginTop="@dimen/activity_horizontal_margin"
            android:layout_marginEnd="@dimen/activity_horizontal_margin"
            android:gravity="start|top"
            android:hint="@string/COMMENT_HINT" />
    </ScrollView>

    <Button
        android:id="@+id/submit"
        style="@style/PrimaryButton"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/activity_horizontal_margin"
        android:layout_marginEnd="@dimen/activity_horizontal_margin"
        android:layout_marginBottom="@dimen/activity_vertical_margin"
        android:enabled="false"
        android:text="@string/SUBMIT_COMMENT"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>

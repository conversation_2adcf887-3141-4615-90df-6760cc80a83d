<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/issue_cell_margin_top"
    android:layout_marginStart="@dimen/cell_margin_lr"
    android:layout_marginEnd="@dimen/cell_margin_lr"
    app:cardElevation="1.5dp"
    app:cardCornerRadius="@dimen/card_corner_radius">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@drawable/card_background"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:id="@+id/issue_status"
            android:layout_width="@dimen/cell_status_width"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            style="@style/IssueCellTitle"
            android:id="@+id/issue_title"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/cell_text_padding"
            android:layout_marginEnd="@dimen/cell_text_padding"
            android:paddingTop="@dimen/cell_text_padding"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/issue_status"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="AS009963" />

        <TextView
            style="@style/IssueCellDescription"
            android:id="@+id/issue_description"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/cell_text_padding"
            android:layout_marginEnd="@dimen/cell_text_padding"
            android:paddingTop="3dp"
            android:paddingBottom="@dimen/cell_text_padding"
            android:paddingEnd="@dimen/cell_text_padding"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/issue_status"
            app:layout_constraintTop_toBottomOf="@id/issue_title"
            tools:text="Nelkenweg 2, 8020 Graz" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>
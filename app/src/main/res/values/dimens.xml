<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="cell_margin_lr">20dp</dimen>
    <dimen name="cell_margin_tb">20dp</dimen>
    <dimen name="cell_status_width">18dp</dimen>
    <dimen name="cell_text_padding">12dp</dimen>
    <dimen name="input_height">48dp</dimen>
    <dimen name="button_height">60dp</dimen>
    <dimen name="input_horizontal_margin">32dp</dimen>
    <dimen name="issue_cell_margin_top">20dp</dimen>
    <dimen name="card_corner_radius">4dp</dimen>
    <dimen name="tab_indicator">5dp</dimen>
    <dimen name="search_bar_end_margin">16dp</dimen>
    <dimen name="search_bar_text_margin">8dp</dimen>
    <dimen name="cell_padding_lr">20dp</dimen>
    <dimen name="list_spacing_after_last_item_small">24dp</dimen>
    <dimen name="cell_margin_small_tb">4dp</dimen>
    <dimen name="cell_padding_tb">10dp</dimen>
    <dimen name="grid_layout_padding">13dp</dimen>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin_double">32dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
</resources>

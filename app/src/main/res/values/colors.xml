<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- color definitions-->
    <color name="primary">#7F8FA4</color>
    <color name="primaryDark">#657890</color>

    <color name="accent">#266BCC</color>

    <color name="border">#DFE9F8</color>

    <color name="label">#B4BDC4</color>
    <color name="content">#1E1E1E</color>

    <color name="black">#000000</color>
    <color name="white">#ffffff</color>
    <color name="neb_light_grey">#E7E7E7</color>
    <color name="neb_grey">#c0c7d0</color>

    <color name="hint">#C2CAD1</color>

    <!-- status colors -->
    <color name="statusDoneTechnicianStart">#54347B</color>
    <color name="statusDoneTechnicianEnd">#8F74B0</color>

    <color name="statusAcceptedStart">#769B29</color>
    <color name="statusAcceptedEnd">#A0D962</color>

    <color name="statusClosedStart">#323232</color>
    <color name="statusClosedEnd">#7B8894</color>

    <color name="statusPlannedStart">#F1B115</color>
    <color name="statusPlannedEnd">#FBE160</color>

    <color name="statusNewStart">#245288</color>
    <color name="statusNewEnd">#6398D7</color>

    <color name="statusReopenedStart">#5E93BC</color>
    <color name="statusReopenedEnd">#89C9F9</color>

    <color name="statusNotStartedStart">#CD1212</color>
    <color name="statusNotStartedEnd">#F8703F</color>

    <!--    State Abwesend Start Color: #D45F01-->
    <!--    State Abwesend End Color: #FF9728-->

    <color name="tabSelected">#B4BDC4</color>
</resources>

<resources>

    <style name="BaseAppTheme" parent="Theme.MaterialComponents.Light.NoActionBar.Bridge">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryDark">@color/primaryDark</item>
        <!--        <item name="colorSecondary">@color/primary</item>-->
        <!--        <item name="colorSecondaryVariant">@color/primary</item>-->
        <item name="colorAccent">@color/accent</item>
        <item name="colorOnPrimary">@android:color/white</item>
        <!--        <item name="colorOnSecondary">@color/content</item>-->
        <item name="colorOnBackground">@color/content</item>
        <item name="colorOnSurface">@color/content</item>
        <item name="android:colorBackground">@android:color/white</item>
        <item name="colorSurface">@android:color/white</item>
        <item name="android:textColor">@color/content</item>
        <item name="android:textColorPrimary">@color/content</item>
        <!--        <item name="android:textColorSecondary">@android:color/white</item>-->
        <item name="android:popupTheme">@style/Popup</item>
        <item name="popupTheme">@style/Popup</item>
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:windowBackground">@android:color/white</item>

    </style>

    <style name="AppTheme" parent="BaseAppTheme"></style>

    <style name="AppTheme.Launcher">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
    </style>

    <style name="Toolbar" parent="ThemeOverlay.MaterialComponents.ActionBar">
        <item name="android:background">@color/primary</item>
        <item name="android:textColorPrimary">@android:color/white</item>
    </style>

    <style name="SearchToolbar" parent="ThemeOverlay.MaterialComponents.ActionBar">
        <item name="android:background">@drawable/searchbar_background</item>
        <item name="android:textColorPrimary">@color/primary</item>
    </style>

    <style name="Popup" parent="ThemeOverlay.MaterialComponents.Light">
        <item name="android:itemBackground">@android:color/white</item>
    </style>

    <style name="PrimaryButton">
        <item name="android:minHeight">@dimen/button_height</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:letterSpacing">0</item>
        <item name="android:fontFamily">@font/open_sans_semibold</item>
        <item name="strokeColor">@color/accent</item>
        <item name="backgroundTint">@color/button_state_color</item>
    </style>

    <style name="SecondaryButton" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:minHeight">@dimen/button_height</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/accent</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:letterSpacing">0</item>
        <item name="android:fontFamily">@font/open_sans_semibold</item>
        <item name="strokeColor">@color/border</item>
        <item name="backgroundTint">@android:color/white</item>
    </style>

    <style name="TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/text_input_border</item>
        <item name="hintTextColor">@color/text_input_border</item>
        <item name="placeholderTextColor">@color/label</item>
        <item name="android:textColorHint">@color/text_input_border</item>
        <item name="helperTextTextColor">@color/primary</item>
        <item name="boxStrokeWidth">1dp</item>
    </style>

    <style name="NebText" parent="AppTheme">
        <item name="android:textColor">@color/black</item>
    </style>

    <style name="IssueCellTitle" parent="NebText">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">17sp</item>
    </style>

    <style name="IssueCellDescription" parent="NebText">
        <item name="android:textSize">15sp</item>
    </style>

    <style name="NebLabel" parent="AppTheme">
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="NebStatusLabel" parent="NebLabel">
        <item name="android:fontFamily">@font/open_sans_extrabold</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="NebLabelTitle" parent="NebLabel">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/open_sans_extrabold</item>
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="NebLabelContent" parent="NebText">
        <item name="android:textSize">16sp</item>
    </style>

    <style name="NebUnsentNotes" parent="NebText">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/label</item>
    </style>

    <style name="SearchField" parent="Widget.AppCompat.EditText">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textCursorDrawable">@null</item>
        <item name="android:background">@null</item>
        <item name="android:lines">1</item>
        <item name="android:maxLines">1</item>
        <item name="android:inputType">text</item>
    </style>

    <style name="ProgressBar" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateTintMode">src_atop</item>
        <item name="android:indeterminateTint">@color/primary</item>
    </style>

    <style name="NoteInputField">
        <item name="android:textColorHint">@color/hint</item>
        <item name="android:background">@null</item>
        <item name="android:inputType">textMultiLine</item>
    </style>


</resources>

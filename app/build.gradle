plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-parcelize'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'

    id 'io.sentry.android.gradle' version '5.9.0' apply false

    id "io.gitlab.arturbosch.detekt" version "1.23.8"
}

apply from: "../config/git-hooks/git-hooks.gradle"
preBuild.dependsOn installGitHooks

android {
    namespace 'com.bytepoets.neubauer'

    defaultConfig {
        applicationId "com.bytepoets.neubauer"
        minSdkVersion 23
        targetSdkVersion 36
        compileSdkVersion 36
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        kapt {
            arguments {
                arg("room.schemaLocation", "${projectDir}/schemas".toString())
            }
        }
    }
    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }

    signingConfigs {
        signing {
            storeFile = file("${System.env.SIGNING_KEYSTORE_FILE}")
            storePassword "${System.env.SIGNING_KEYSTORE_PASSWORD}"

            keyAlias "${System.env.SIGNING_KEY_ALIAS}"
            keyPassword "${System.env.SIGNING_KEYSTORE_PASSWORD}"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.signing

            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {}
    }

    buildFeatures {
        viewBinding true
        buildConfig true
    }

    flavorDimensions "version"

    productFlavors {
        local {
            getProps('../config/local.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            buildConfigField 'boolean', 'SHOW_DEBUG_MENU', 'Boolean.parseBoolean("true")'
        }
        daily {
            getProps('../config/daily.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            buildConfigField 'boolean', 'SHOW_DEBUG_MENU', 'Boolean.parseBoolean("true")'
        }
        staging {
            getProps('../config/staging.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            buildConfigField 'boolean', 'SHOW_DEBUG_MENU', 'Boolean.parseBoolean("true")'
        }
        production {
            getProps('../config/production.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            buildConfigField 'boolean', 'SHOW_DEBUG_MENU', 'Boolean.parseBoolean("false")'
        }
    }
}

detekt {
    buildUponDefaultConfig = true
    autoCorrect = true
    input = files("src")
    config = files("../config/detekt.yml")
}

tasks.withType(io.gitlab.arturbosch.detekt.Detekt) {
    exclude("**/resources/**")
    exclude("**/build/**")
}

dependencies {
    def retrofit_version = "2.12.0"
    def moshi_version = "1.15.2"
    def room_version = "2.7.2"
    def nav_version = "2.9.3"
    def glide_version = "4.16.0"
    def dagger_version = "2.57.1"

    dailyImplementation project(":unsecuressl")
    localImplementation project(":unsecuressl")

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.core:core-ktx:1.17.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.2.0"
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.2.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation "androidx.fragment:fragment-ktx:1.8.9"
    implementation 'com.pnikosis:materialish-progress:1.7'
    implementation "androidx.navigation:navigation-fragment-ktx:$nav_version"
    implementation "androidx.navigation:navigation-ui-ktx:$nav_version"
    implementation "androidx.navigation:navigation-fragment:$nav_version"
    implementation "androidx.navigation:navigation-ui:$nav_version"
    implementation "androidx.work:work-runtime-ktx:2.7.0"

    implementation "com.google.dagger:dagger:$dagger_version"
    implementation "com.google.dagger:dagger-android:$dagger_version"
    implementation "com.google.dagger:dagger-android-support:$dagger_version"
    kapt "com.google.dagger:dagger-compiler:$dagger_version"
    kapt "com.google.dagger:dagger-android-processor:$dagger_version"

    implementation 'io.sentry:sentry-android:8.20.0'

    implementation 'com.jakewharton.timber:timber:5.0.1'
    implementation "com.squareup.moshi:moshi:$moshi_version"
    implementation "com.squareup.moshi:moshi-adapters:$moshi_version"
    implementation "com.squareup.moshi:moshi-kotlin:$moshi_version"
    kapt "com.squareup.moshi:moshi-kotlin-codegen:$moshi_version"
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-scalars:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-moshi:$retrofit_version"

    kapt "androidx.room:room-compiler:$room_version"
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"

    detektPlugins "io.gitlab.arturbosch.detekt:detekt-formatting:1.23.8"

    testImplementation 'junit:junit:4.13.2'
    testImplementation 'com.google.truth:truth:1.4.4'
    testImplementation 'org.mockito:mockito-core:5.19.0'
    testImplementation 'androidx.arch.core:core-testing:2.2.0'
    testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'

    implementation "com.github.bumptech.glide:glide:$glide_version"
    kapt "com.github.bumptech.glide:compiler:$glide_version"

    androidTestImplementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    androidTestImplementation 'androidx.test:core:1.7.0'
    androidTestImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.3.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.7.0'
    androidTestImplementation 'androidx.test:runner:1.7.0'
    androidTestImplementation 'androidx.test:rules:1.7.0'
    androidTestImplementation 'org.hamcrest:hamcrest-library:3.0'
    androidTestImplementation 'androidx.arch.core:core-testing:2.2.0'
    androidTestImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.10.2'
    androidTestImplementation 'com.google.truth:truth:1.4.4'

    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.5'
}

// settings for multiple environments
// https://medium.com/@juhi.matta/how-to-set-up-build-environment-in-android-ac06f74df51a
def getProps(path) {
    Properties props = new Properties()
    props.load(new FileInputStream(file(path)))
    return props
}

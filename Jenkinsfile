pipeline {
    agent {
        label 'android'
    }
    options {
        timestamps()
        disableConcurrentBuilds()
    }
    parameters {
        booleanParam(name: 'PROCEED_EVEN_IF_VERIFICATION_STEP_FAILS', defaultValue: false, description: 'Allow build and deployment when tests fail')
        booleanParam(name: 'DEPLOY_DEVELOPMENT_ALWAYS', defaultValue: false, description: 'Check if you want to deploy development version from all branches')
        booleanParam(name: 'DEPLOY_STAGING_ALWAYS', defaultValue: false, description: 'Check if you want to deploy staging version from all branches')
        booleanParam(name: 'DEPLOY_PRODUCTION_ALWAYS', defaultValue: false, description: 'Check if you want to deploy production version from all branches')
    }
    environment {
        SIGNING_CONFIGURATION_FOLDER = "${REMOTE_ROOT_DIRECTORY}/secrets/android-signing/android-keystores/neubauer_android/"
        SIGNING_CONFIGURATION_FILE = "${SIGNING_CONFIGURATION_FOLDER}/neubauer_android_signing_config.gradle"
        SIGNING_CONFIGURATION_KEYSTORE = "${SIGNING_CONFIGURATION_FOLDER}/neubauer_android_keystore.jks"
        REMOTE_USER = 'ssh-771063-apps'
        REMOTE_HOST = 'apps.bytepoets.com'
        REMOTE_BASEDIR = '/kunden/318575_8010/webhosting/internal/bytepoets/apps.bytepoets.com/packages'
        REMOTE_PROJECT_FOLDER = 'neubauer-android'
        BYTEPOETS_NEXUS = credentials('bytepoets.nexus.credentials')
    }
    stages {
        stage("Compile") {
            steps {
                sh "./gradlew clean compileDailyReleaseSources -PdisablePreDex -PbytepoetsNexusUsername=${BYTEPOETS_NEXUS_USR} -PbytepoetsNexusPassword=${BYTEPOETS_NEXUS_PSW}"
            }
        }

        stage("Verification") {
            steps {
                sh "./gradlew lintDailyRelease detekt testDailyDebugUnitTest -PdisablePreDex -PbytepoetsNexusUsername=${BYTEPOETS_NEXUS_USR} -PbytepoetsNexusPassword=${BYTEPOETS_NEXUS_PSW} || ${params.PROCEED_EVEN_IF_VERIFICATION_STEP_FAILS}"
            }
        }

        stage("Build APK") {
            when {
                anyOf {
                    branch 'master'
                    branch 'develop'
                    environment name: 'DEPLOY_DEVELOPMENT_ALWAYS', value: 'true'
                    environment name: 'DEPLOY_STAGING_ALWAYS', value: 'true'
                    environment name: 'DEPLOY_PRODUCTION_ALWAYS', value: 'true'
                }
            }
            steps {
                sh "./gradlew assembleRelease -PdisablePreDex -PbytepoetsNexusUsername=${BYTEPOETS_NEXUS_USR} -PbytepoetsNexusPassword=${BYTEPOETS_NEXUS_PSW}"
            }
        }

        stage("Copy Development APK to App Hosting Server") {
            when {
                anyOf {
                    branch 'master'
                    branch 'develop'
                    environment name: 'DEPLOY_DEVELOPMENT_ALWAYS', value: 'true'
                }
            }
            steps {
                script {
                    sshagent(credentials: ['bytepoets.nexus.credentials']) {
                        def sshCommand = "ssh -oStrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST}"
                        def scpCommand = "scp -oStrictHostKeyChecking=no"
                        def dirName = generateDirName()
                        def remoteDir = "${REMOTE_BASEDIR}/${REMOTE_PROJECT_FOLDER}/${dirName}"
                        sh """${sshCommand} mkdir ${remoteDir}"""

                        sh """${scpCommand} app/build/outputs/apk/daily/release/*-release.apk ${REMOTE_USER}@${REMOTE_HOST}:${remoteDir}"""
                    }
                }
            }
        }

        stage("Copy Staging APK to App Hosting Server") {
                    when {
                        anyOf {
                            branch 'master'
                            environment name: 'DEPLOY_STAGING_ALWAYS', value: 'true'
                        }
                    }
                    steps {
                        script {
                            sshagent(credentials: ['bytepoets.nexus.credentials']) {
                                def sshCommand = "ssh -oStrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST}"
                                def scpCommand = "scp -oStrictHostKeyChecking=no"
                                def dirName = generateDirName()
                                def remoteDir = "${REMOTE_BASEDIR}/${REMOTE_PROJECT_FOLDER}/${dirName}"
                                sh """${sshCommand} mkdir ${remoteDir} || true"""

                                sh """${scpCommand} app/build/outputs/apk/staging/release/*-release.apk ${REMOTE_USER}@${REMOTE_HOST}:${remoteDir}"""
                            }
                        }
                    }
                }

        stage("Copy Production APK to App Hosting Server") {
            when {
                anyOf {
                    branch 'master'
                    environment name: 'DEPLOY_PRODUCTION_ALWAYS', value: 'true'
                }
            }
            steps {
                script {
                    sshagent(credentials: ['bytepoets.nexus.credentials']) {
                        def sshCommand = "ssh -oStrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST}"
                        def scpCommand = "scp -oStrictHostKeyChecking=no"
                        def dirName = generateDirName()
                        def remoteDir = "${REMOTE_BASEDIR}/${REMOTE_PROJECT_FOLDER}/${dirName}"
                        sh """${sshCommand} mkdir ${remoteDir} || true"""

                        sh """${scpCommand} app/build/outputs/apk/production/release/*-release.apk ${REMOTE_USER}@${REMOTE_HOST}:${remoteDir}"""
                    }
                }
            }
        }
    }
    post {
        always {
            junit 'app/build/test-results/**/*.xml'
            recordIssues enabledForFailure: true, tools: [
                detekt(pattern: 'app/build/reports/detekt/detekt.xml'),
                androidLintParser(pattern: 'app/build/reports/**/**.xml')
            ]
        }
        fixed {
            script {
                def slackAttachments = [
                    [
                        fallback: "Open on Jenkins ${env.BUILD_URL}",
                        title: "Jenkins - ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                        title_link: "${env.BUILD_URL}",
                        color: "good",
                        actions: [
                          [
                              type: 'button',
                              text: 'Open on Jenkins',
                              url: "${env.BUILD_URL}"
                          ]
                        ],
                        fields: [
                            [
                                title: 'Branch',
                                value: "${env.BRANCH_NAME}",
                                short: false
                            ],
                            [
                                title: 'Commit',
                                value: "${env.GIT_COMMIT}",
                                short: false
                            ],
                            [
                                title: 'Author',
                                value: "${env.GIT_AUTHOR_NAME}",
                                short: false
                            ]
                        ]
                    ]
                ]
                slackSend message: "Everything is fine again! :sunny:", attachments: slackAttachments
            }
        }
        regression {
            script {
                def slackAttachments = [
                    [
                        fallback: "Open on Jenkins ${env.BUILD_URL}",
                        title: "Jenkins - ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                        title_link: "${env.BUILD_URL}",
                        color: "danger",
                        actions: [
                          [
                              type: 'button',
                              text: 'Open on Jenkins',
                              url: "${env.BUILD_URL}"
                          ]
                        ],
                        fields: [
                            [
                                title: 'Branch',
                                value: "${env.BRANCH_NAME}",
                                short: false
                            ],
                            [
                                title: 'Commit',
                                value: "${env.GIT_COMMIT}",
                                short: false
                            ],
                            [
                                title: 'Author',
                                value: "${env.GIT_AUTHOR_NAME}",
                                short: false
                            ]
                        ]
                    ]
                ]
                slackSend message: "The build status went to failure :shit:", attachments: slackAttachments
            }
        }
    }
}

def generateDirName() {
    def date = new Date()
    def sdf = new java.text.SimpleDateFormat("yyyy-MM-dd_HH-mm")
    return sdf.format(date)
}
